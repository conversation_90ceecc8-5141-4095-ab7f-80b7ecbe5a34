# Referral System Migration Scripts

This directory contains scripts to set up and migrate the referral system for Togeda.ai.

## Scripts Overview

### 1. Complete Setup Script

```bash
# Using default .env file
npm run setup:referrals

# Using production environment
npm run setup:referrals:prod

# Using staging environment
npm run setup:referrals:staging

# Using custom .env file
npx tsx scripts/setup-referral-system.ts .env.custom

# Using environment variable
ENV_FILE=.env.custom npm run setup:referrals
```

Runs the complete referral system setup including:

- Initializing global perk definitions
- Generating referral codes for existing users
- Validating the setup

### 2. Individual Scripts

#### Initialize Global Perks

```bash
# Using default .env file
npm run init:global-perks

# Using production environment
npm run init:global-perks:prod

# Using staging environment
npm run init:global-perks:staging

# Using custom .env file
npx tsx scripts/initialize-global-perks.ts .env.custom
```

Creates the default global perk definitions in Firestore:

- 5 referrals = 1 free squad (permanent)
- 10 referrals = 2 months free Pro subscription

#### Migrate Referral Codes

```bash
# Using default .env file
npm run migrate:referral-codes

# Using production environment
npm run migrate:referral-codes:prod

# Using staging environment
npm run migrate:referral-codes:staging

# Using custom .env file
npx tsx scripts/migrate-referral-codes.ts .env.custom
```

Generates unique 8-character alphanumeric referral codes for all existing users who don't have one.

## Prerequisites

1. **Environment Variables**: Ensure you have the following environment variables set in your .env file(s):

   ```
   FIREBASE_SERVICE_ACCOUNT_KEY=<your-firebase-service-account-json>
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=<your-project-id>
   ```

   **Environment File Options**:

   - `.env` - Default development environment
   - `.env.staging` - Staging environment
   - `.env.production` - Production environment
   - `.env.custom` - Any custom environment file

   **Ways to specify environment file**:

   1. **Command line argument**: `npx tsx script.ts .env.production`
   2. **Environment variable**: `ENV_FILE=.env.staging npm run script`
   3. **NPM script shortcuts**: `npm run script:prod` or `npm run script:staging`

2. **Firebase Admin SDK**: The scripts use Firebase Admin SDK for server-side operations.

3. **Dependencies**: Make sure you have `tsx` installed for running TypeScript scripts:
   ```bash
   npm install -g tsx
   ```

## Running the Migration

### For New Deployments

Run the complete setup script:

```bash
npm run setup:referrals
```

### For Existing Deployments

If you need to run individual parts:

1. **First time setup** - Initialize global perks:

   ```bash
   npm run init:global-perks
   ```

2. **Migrate existing users** - Generate referral codes:
   ```bash
   npm run migrate:referral-codes
   ```

## Safety Features

- **Idempotent**: All scripts can be safely re-run without duplicating data
- **Error Handling**: Scripts include comprehensive error handling and logging
- **Validation**: Each script includes validation to verify successful completion
- **Non-destructive**: Scripts only create new data, never modify or delete existing data

## Expected Output

### Successful Migration

```
🚀 Setting up referral system...
==================================================

📋 Step 1: Initializing global perk definitions...
Creating new perk squad_perk_5_referrals...
Creating new perk subscription_perk_10_referrals...
Successfully initialized 2 global perks

Validating global perk initialization...
Found 2 global perks
- squad_perk_5_referrals: 1 Free Squad (5 referrals)
- subscription_perk_10_referrals: 2 Months Free Pro (10 referrals)
✅ Global perk initialization successful

👥 Step 2: Generating referral codes for existing users...
Found 150 users
Created referral code ABC12345 for user user123 (<EMAIL>)
...
Processed: 150 users
Skipped: 0 users (already had codes)
Errors: 0 users

Validating migration results...
Total users: 150
Total referral codes: 150
✅ Migration validation successful: All users have referral codes

✅ Referral system setup completed successfully!
```

## Troubleshooting

### Common Issues

1. **Firebase Authentication Error**

   - Verify your `FIREBASE_SERVICE_ACCOUNT_KEY` is correctly set
   - Ensure the service account has the necessary permissions

2. **Project ID Error**

   - Check that `NEXT_PUBLIC_FIREBASE_PROJECT_ID` matches your Firebase project

3. **Permission Errors**
   - Ensure the Firebase service account has Firestore read/write permissions

### Manual Verification

After running the scripts, you can verify the setup:

1. **Check Global Perks** in Firebase Console:

   - Navigate to Firestore
   - Look for `referral/perks` collection
   - Verify 2 perk documents exist

2. **Check Referral Codes**:

   - Look for `referral` collection
   - Verify each document has `userId`, `totalReferrals`, and `isActive` fields

3. **Test the System**:
   - Create a new user account with a referral code
   - Verify the referral is processed correctly
   - Check that perks are unlocked at the correct thresholds

## File Structure

```
scripts/
├── README.md                     # This file
├── setup-referral-system.ts      # Complete setup script
├── initialize-global-perks.ts    # Global perk initialization
└── migrate-referral-codes.ts     # Referral code migration
```

## Next Steps

After successful migration:

1. Deploy the updated application
2. Test the referral flow with new user signups
3. Verify existing users can access their referral codes in Settings > Referrals
4. Monitor the system for any issues
5. Test perk unlocking by completing actual referrals
