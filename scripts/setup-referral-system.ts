/**
 * Complete setup script for the referral system
 *
 * This script:
 * 1. Initializes global perk definitions
 * 2. Generates referral codes for existing users
 * 3. Validates the setup
 *
 * Usage:
 * npm run setup:referrals
 * npm run setup:referrals .env.production
 * ENV_FILE=.env.staging npm run setup:referrals
 */

import { migrateReferralCodes, validateMigration } from "./migrate-referral-codes"
import { initializeGlobalPerks, validateInitialization } from "./initialize-global-perks"

async function setupReferralSystem() {
  console.log("🚀 Setting up referral system...")
  console.log("=".repeat(50))

  try {
    // Step 1: Initialize global perks
    console.log("\n📋 Step 1: Initializing global perk definitions...")
    await initializeGlobalPerks()
    await validateInitialization()

    // Step 2: Migrate referral codes for existing users
    console.log("\n👥 Step 2: Generating referral codes for existing users...")
    await migrateReferralCodes()
    await validateMigration()

    console.log("\n✅ Referral system setup completed successfully!")
    console.log("\nNext steps:")
    console.log("1. Deploy the updated application")
    console.log("2. Test the referral flow with a new user signup")
    console.log(
      "3. Verify that existing users can see their referral codes in Settings > Referrals"
    )
    console.log("4. Test perk unlocking by completing referrals")
  } catch (error) {
    console.error("\n❌ Referral system setup failed:", error)
    process.exit(1)
  }
}

// Run the setup if this script is executed directly
if (require.main === module) {
  setupReferralSystem()
    .then(() => {
      console.log("\n🎉 Setup complete!")
      process.exit(0)
    })
    .catch((error) => {
      console.error("\n💥 Setup failed:", error)
      process.exit(1)
    })
}

export { setupReferralSystem }
